import { Player } from "../player";
import Viewer from "@interactify/infinite-viewer";
import { useRef, useMemo } from "react";
import useStore from "../store/use-store";
import StateManager from "@designcombo/state";
import SceneEmpty from "./empty";
import Board from "./board";
import useZoom from "../hooks/use-zoom";
import { SceneInteractions } from "./interactions";

export default function Scene({
  stateManager,
}: {
  stateManager: StateManager;
}) {
  const viewerRef = useRef<Viewer>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { size, trackItemIds } = useStore();
  const { zoom, handlePinch } = useZoom(containerRef, viewerRef, size);

  // Memoize container styles for better performance
  const containerStyles = useMemo(() => ({
    width: "100%",
    height: "100%",
    position: "relative" as const,
    flex: 1,
    // Performance optimizations
    willChange: "transform",
    transform: "translateZ(0)", // Force hardware acceleration
  }), []);

  // Memoize viewer props for better performance
  const viewerProps = useMemo(() => ({
    className: "player-container bg-sidebar",
    displayHorizontalScroll: false,
    displayVerticalScroll: false,
    zoom,
    usePinch: true,
    pinchThreshold: 50,
    onPinch: handlePinch,
  }), [zoom, handlePinch]);

  return (
    <div
      style={containerStyles}
      ref={containerRef}
    >
      {trackItemIds.length === 0 && <SceneEmpty />}
      <Viewer
        ref={viewerRef}
        {...viewerProps}
      >
        <Board size={size}>
          <Player />
          <SceneInteractions
            stateManager={stateManager}
            viewerRef={viewerRef}
            containerRef={containerRef}
            zoom={zoom}
            size={size}
          />
        </Board>
      </Viewer>
    </div>
  );
}
