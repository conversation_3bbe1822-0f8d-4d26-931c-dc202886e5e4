import { useState, useMemo } from "react";
import { DroppableArea } from "./droppable";
import useStore from "../store/use-store";

const SceneBoard = ({
  size,
  children,
}: {
  size: { width: number; height: number };
  children: React.ReactNode;
}) => {
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const { backgroundColor } = useStore();

  // Memoize styles for better performance
  const droppableStyle = useMemo(() => ({
    width: size.width,
    height: size.height,
    // Performance optimizations
    willChange: "transform",
    transform: "translateZ(0)", // Force hardware acceleration
  }), [size.width, size.height]);

  const overlayStyle = useMemo(() => ({
    width: size.width,
    height: size.height,
    boxShadow: `0 0 0 5000px ${backgroundColor}`,
    // Performance optimizations
    willChange: "transform, opacity",
    transform: "translateZ(0)", // Force hardware acceleration
  }), [size.width, size.height, backgroundColor]);

  const overlayClassName = useMemo(() =>
    `pointer-events-none absolute z-50 border border-white/15 transition-colors duration-200 ease-in-out ${
      isDraggingOver ? "border-4 border-dashed border-white bg-white/[0.075]" : "bg-transparent"
    }`,
    [isDraggingOver]
  );

  return (
    <DroppableArea
      id="artboard"
      onDragStateChange={setIsDraggingOver}
      style={droppableStyle}
      className="pointer-events-auto"
    >
      <div
        style={overlayStyle}
        className={overlayClassName}
      />
      {children}
    </DroppableArea>
  );
};

export default SceneBoard;
